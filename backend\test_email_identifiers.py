#!/usr/bin/env python3
"""
测试邮件标识传递功能
验证message_id和server_uid是否正确提取和保存
"""

import sys
import os
sys.path.append('.')

from utils.email.common import parse_email_message
from database.db import Database
import email
from datetime import datetime, timezone, timedelta
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_test_email():
    """创建一个测试邮件对象"""
    # 创建一个简单的邮件
    msg = email.message.EmailMessage()
    msg['Subject'] = '测试邮件 - 标识传递测试'
    msg['From'] = '<EMAIL>'
    msg['To'] = '<EMAIL>'
    msg['Date'] = email.utils.formatdate(localtime=True)
    msg['Message-ID'] = '<<EMAIL>>'
    msg.set_content('这是一封测试邮件，用于验证邮件标识传递功能。')
    
    return msg

def test_parse_email_message():
    """测试parse_email_message函数是否正确提取message_id"""
    print("=" * 60)
    print("测试parse_email_message函数")
    print("=" * 60)
    
    try:
        # 创建测试邮件
        msg = create_test_email()
        
        print(f"测试邮件信息:")
        print(f"  主题: {msg['Subject']}")
        print(f"  发件人: {msg['From']}")
        print(f"  Message-ID: {msg['Message-ID']}")
        
        # 解析邮件
        mail_record = parse_email_message(msg, "INBOX")
        
        if mail_record:
            print(f"\n解析结果:")
            print(f"  主题: {mail_record.get('subject')}")
            print(f"  发件人: {mail_record.get('sender')}")
            print(f"  Message-ID: {mail_record.get('message_id')}")
            print(f"  Server-UID: {mail_record.get('server_uid')}")
            
            # 检查message_id是否正确提取
            expected_message_id = '<EMAIL>'
            actual_message_id = mail_record.get('message_id')
            
            if actual_message_id == expected_message_id:
                print(f"  ✓ Message-ID正确提取: {actual_message_id}")
                return True
            else:
                print(f"  ✗ Message-ID提取错误: 期望={expected_message_id}, 实际={actual_message_id}")
                return False
        else:
            print("  ✗ 邮件解析失败")
            return False
            
    except Exception as e:
        print(f"  ✗ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_save_with_identifiers():
    """测试数据库保存时是否正确处理邮件标识"""
    print("\n" + "=" * 60)
    print("测试数据库保存邮件标识")
    print("=" * 60)
    
    try:
        db = Database()
        
        # 测试数据
        test_email_id = 1
        test_subject = "测试邮件 - 数据库标识测试"
        test_sender = "<EMAIL>"
        test_content = "这是一封测试邮件内容"
        test_message_id = "<EMAIL>"
        test_server_uid = "67890"
        
        print(f"测试数据:")
        print(f"  主题: {test_subject}")
        print(f"  Message-ID: {test_message_id}")
        print(f"  Server-UID: {test_server_uid}")
        
        # 保存邮件记录
        success, mail_id = db.add_mail_record(
            email_id=test_email_id,
            subject=test_subject,
            sender=test_sender,
            received_time=datetime.now(),
            content=test_content,
            message_id=test_message_id,
            server_uid=test_server_uid
        )
        
        if success and mail_id:
            print(f"\n✓ 邮件保存成功，ID: {mail_id}")
            
            # 验证保存的数据
            cursor = db.conn.execute(
                "SELECT message_id, server_uid, mail_hash FROM mail_records WHERE id = ?",
                (mail_id,)
            )
            record = cursor.fetchone()
            
            if record:
                saved_message_id = record[0]
                saved_server_uid = record[1]
                saved_mail_hash = record[2]
                
                print(f"数据库中的记录:")
                print(f"  Message-ID: {saved_message_id}")
                print(f"  Server-UID: {saved_server_uid}")
                print(f"  Mail-Hash: {saved_mail_hash[:16]}...")
                
                # 验证数据
                if saved_message_id == test_message_id and saved_server_uid == test_server_uid:
                    print("  ✓ 邮件标识正确保存到数据库")
                    
                    # 清理测试数据
                    db.conn.execute("DELETE FROM mail_records WHERE id = ?", (mail_id,))
                    db.conn.commit()
                    print("  ✓ 测试数据已清理")
                    
                    return True
                else:
                    print("  ✗ 邮件标识保存错误")
                    return False
            else:
                print("  ✗ 无法从数据库读取保存的记录")
                return False
        else:
            print(f"  ✗ 邮件保存失败: success={success}, mail_id={mail_id}")
            return False
            
    except Exception as e:
        print(f"  ✗ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_duplicate_detection():
    """测试基于邮件标识的重复检测"""
    print("\n" + "=" * 60)
    print("测试邮件标识重复检测")
    print("=" * 60)
    
    try:
        db = Database()
        
        # 测试数据
        test_email_id = 1
        test_subject = "测试邮件 - 重复检测测试"
        test_sender = "<EMAIL>"
        test_content = "这是一封测试邮件内容"
        test_message_id = "<EMAIL>"
        test_server_uid = "11111"
        
        print(f"测试重复检测:")
        print(f"  Message-ID: {test_message_id}")
        print(f"  Server-UID: {test_server_uid}")
        
        # 第一次保存
        success1, mail_id1 = db.add_mail_record(
            email_id=test_email_id,
            subject=test_subject,
            sender=test_sender,
            received_time=datetime.now(),
            content=test_content,
            message_id=test_message_id,
            server_uid=test_server_uid
        )
        
        print(f"\n第一次保存: success={success1}, mail_id={mail_id1}")
        
        # 第二次保存相同的message_id
        success2, mail_id2 = db.add_mail_record(
            email_id=test_email_id,
            subject=test_subject + " (重复)",
            sender=test_sender,
            received_time=datetime.now(),
            content=test_content + " (重复)",
            message_id=test_message_id,  # 相同的message_id
            server_uid="22222"  # 不同的server_uid
        )
        
        print(f"第二次保存: success={success2}, mail_id={mail_id2}")
        
        # 验证结果
        if success1 and not success2:
            print("✓ 基于Message-ID的重复检测工作正常")
            
            # 清理测试数据
            if mail_id1:
                db.conn.execute("DELETE FROM mail_records WHERE id = ?", (mail_id1,))
                db.conn.commit()
                print("✓ 测试数据已清理")
            
            return True
        else:
            print("✗ 重复检测存在问题")
            return False
            
    except Exception as e:
        print(f"✗ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试邮件标识传递功能...\n")
    
    tests = [
        ("邮件解析", test_parse_email_message),
        ("数据库保存", test_database_save_with_identifiers),
        ("重复检测", test_duplicate_detection),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"测试 {test_name} 出现异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！邮件标识传递功能正常工作")
        return True
    else:
        print(f"\n❌ {total - passed} 个测试失败，需要修复问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
