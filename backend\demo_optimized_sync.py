#!/usr/bin/env python3
"""
演示优化后的邮件同步机制
展示Message-ID和Server-UID的重复检测效果
"""

import sys
import os
from datetime import datetime, timedelta

sys.path.append('.')

from database.db import Database
from utils.email.mail_precheck import Mail<PERSON><PERSON><PERSON>he<PERSON>
from utils.email.common import get_china_now
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_sample_emails():
    """创建示例邮件数据"""
    base_time = get_china_now()
    
    emails = [
        {
            'message_id': '<EMAIL>',
            'server_uid': '12345',
            'subject': '重要会议通知',
            'sender': '<EMAIL>',
            'received_time': base_time,
            'content': '明天下午2点在会议室A召开季度总结会议',
            'folder': 'INBOX'
        },
        {
            'message_id': '<EMAIL>',
            'server_uid': '12346',
            'subject': '项目进度更新',
            'sender': '<EMAIL>',
            'received_time': base_time + timedelta(minutes=30),
            'content': '项目当前进度已完成60%，预计下周完成',
            'folder': 'INBOX'
        },
        {
            'message_id': '<EMAIL>',
            'server_uid': '12347',
            'subject': '系统维护通知',
            'sender': '<EMAIL>',
            'received_time': base_time + timedelta(hours=1),
            'content': '系统将于本周末进行维护升级',
            'folder': 'INBOX'
        },
        {
            'message_id': None,  # 没有Message-ID
            'server_uid': '12348',
            'subject': '无Message-ID的邮件',
            'sender': '<EMAIL>',
            'received_time': base_time + timedelta(hours=2),
            'content': '这封邮件没有Message-ID，只有Server-UID',
            'folder': 'INBOX'
        },
        {
            'message_id': None,  # 没有Message-ID
            'server_uid': None,  # 也没有Server-UID
            'subject': '只能用哈希检测的邮件',
            'sender': '<EMAIL>',
            'received_time': base_time + timedelta(hours=3),
            'content': '这封邮件既没有Message-ID也没有Server-UID，只能依靠哈希检测',
            'folder': 'INBOX'
        }
    ]
    
    return emails

def demonstrate_sync_process():
    """演示同步过程"""
    print("=" * 60)
    print("邮件同步机制演示")
    print("=" * 60)
    
    db = Database()
    precheck = MailPreChecker(db)
    email_id = 1  # 假设邮箱ID为1
    
    # 创建示例邮件
    emails = create_sample_emails()
    
    print(f"准备同步 {len(emails)} 封邮件:")
    for i, email in enumerate(emails, 1):
        print(f"  {i}. {email['subject']}")
        print(f"     Message-ID: {email['message_id'] or '(无)'}")
        print(f"     Server-UID: {email['server_uid'] or '(无)'}")
        print(f"     发件人: {email['sender']}")
        print()
    
    print("第一次同步 - 所有邮件都是新的")
    print("-" * 40)
    
    # 第一次同步
    for i, email in enumerate(emails, 1):
        print(f"正在处理第 {i} 封邮件: {email['subject']}")
        
        success, mail_id = db.add_mail_record(
            email_id=email_id,
            subject=email['subject'],
            sender=email['sender'],
            received_time=email['received_time'],
            content=email['content'],
            folder=email['folder'],
            has_attachments=0,
            message_id=email['message_id'],
            server_uid=email['server_uid']
        )
        
        if success:
            print(f"  ✓ 邮件添加成功 (ID: {mail_id})")
        else:
            print(f"  ✗ 邮件已存在，跳过")
        print()
    
    print("第二次同步 - 模拟重复同步")
    print("-" * 40)
    
    # 第二次同步（模拟重复）
    for i, email in enumerate(emails, 1):
        print(f"正在处理第 {i} 封邮件: {email['subject']}")
        
        success, mail_id = db.add_mail_record(
            email_id=email_id,
            subject=email['subject'],
            sender=email['sender'],
            received_time=email['received_time'],
            content=email['content'],
            folder=email['folder'],
            has_attachments=0,
            message_id=email['message_id'],
            server_uid=email['server_uid']
        )
        
        if success:
            print(f"  ✓ 邮件添加成功 (ID: {mail_id})")
        else:
            print(f"  ✗ 邮件已存在，跳过同步")
        print()

def demonstrate_batch_precheck():
    """演示批量预检查"""
    print("=" * 60)
    print("批量预检查演示")
    print("=" * 60)
    
    db = Database()
    precheck = MailPreChecker(db)
    email_id = 1
    
    # 创建混合邮件列表（包含新邮件和已存在邮件）
    existing_emails = create_sample_emails()  # 已存在的邮件
    
    new_emails = [
        {
            'message_id': '<EMAIL>',
            'server_uid': '99991',
            'subject': '新邮件1',
            'sender': '<EMAIL>',
            'received_time': get_china_now(),
            'content': '这是一封新邮件',
            'folder': 'INBOX'
        },
        {
            'message_id': '<EMAIL>',
            'server_uid': '99992',
            'subject': '新邮件2',
            'sender': '<EMAIL>',
            'received_time': get_china_now(),
            'content': '这是另一封新邮件',
            'folder': 'INBOX'
        }
    ]
    
    # 合并邮件列表
    all_emails = existing_emails + new_emails
    
    print(f"准备进行批量预检查，总邮件数: {len(all_emails)}")
    print(f"其中已存在邮件: {len(existing_emails)} 封")
    print(f"其中新邮件: {len(new_emails)} 封")
    print()
    
    # 执行批量预检查
    print("执行批量预检查...")
    filtered_emails = precheck.filter_new_mails(email_id, all_emails)
    
    print(f"预检查结果:")
    print(f"  - 需要处理的新邮件: {len(filtered_emails)} 封")
    print(f"  - 跳过的已存在邮件: {len(all_emails) - len(filtered_emails)} 封")
    print()
    
    if filtered_emails:
        print("新邮件列表:")
        for email in filtered_emails:
            print(f"  - {email['subject']} (发件人: {email['sender']})")

def demonstrate_identifier_priority():
    """演示标识符优先级"""
    print("=" * 60)
    print("标识符优先级演示")
    print("=" * 60)
    
    db = Database()
    email_id = 1
    
    # 测试邮件
    test_email = {
        'message_id': '<EMAIL>',
        'server_uid': '88888',
        'subject': '优先级测试邮件',
        'sender': '<EMAIL>',
        'received_time': get_china_now(),
        'content': '用于测试标识符优先级的邮件',
        'folder': 'INBOX'
    }
    
    print("1. 插入原始邮件")
    success1, mail_id1 = db.add_mail_record(
        email_id=email_id,
        subject=test_email['subject'],
        sender=test_email['sender'],
        received_time=test_email['received_time'],
        content=test_email['content'],
        folder=test_email['folder'],
        has_attachments=0,
        message_id=test_email['message_id'],
        server_uid=test_email['server_uid']
    )
    print(f"   结果: {'成功' if success1 else '失败'} (ID: {mail_id1})")
    print()
    
    print("2. 尝试插入相同Message-ID但不同Server-UID的邮件")
    success2, mail_id2 = db.add_mail_record(
        email_id=email_id,
        subject="不同的主题",
        sender="<EMAIL>",
        received_time=get_china_now(),
        content="不同的内容",
        folder='INBOX',
        has_attachments=0,
        message_id=test_email['message_id'],  # 相同的Message-ID
        server_uid='77777'  # 不同的Server-UID
    )
    print(f"   结果: {'成功' if success2 else '失败（预期，Message-ID优先）'}")
    print()
    
    print("3. 尝试插入不同Message-ID但相同Server-UID的邮件")
    success3, mail_id3 = db.add_mail_record(
        email_id=email_id,
        subject="又一个不同的主题",
        sender="<EMAIL>",
        received_time=get_china_now(),
        content="又一个不同的内容",
        folder='INBOX',
        has_attachments=0,
        message_id='<EMAIL>',  # 不同的Message-ID
        server_uid=test_email['server_uid']  # 相同的Server-UID
    )
    print(f"   结果: {'成功' if success3 else '失败（预期，Server-UID检测）'}")

def main():
    """主演示函数"""
    print("优化后的邮件同步机制演示")
    print("支持Message-ID和Server-UID的多层重复检测")
    print()
    
    try:
        # 演示基本同步过程
        demonstrate_sync_process()
        
        print("\n" + "=" * 60 + "\n")
        
        # 演示批量预检查
        demonstrate_batch_precheck()
        
        print("\n" + "=" * 60 + "\n")
        
        # 演示标识符优先级
        demonstrate_identifier_priority()
        
        print("\n" + "=" * 60)
        print("演示完成！")
        print("=" * 60)
        
    except Exception as e:
        logger.error(f"演示过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
