#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
邮件预检查模块
在fetch邮件之前，通过查询现有的message_id、server_uid或mail_hash来判断邮件是否已存在，
避免不必要的fetch操作，提高邮件同步效率。
"""

import logging
from typing import List, Dict, Set, Tuple, Optional
from .common import generate_mail_hash

logger = logging.getLogger(__name__)

class MailPreChecker:
    """邮件预检查器"""
    
    def __init__(self, db):
        self.db = db
    
    def check_mail_exists_by_identifiers(self, email_id: int, message_id: str = None, 
                                       server_uid: str = None, subject: str = None, 
                                       sender: str = None, received_time = None, 
                                       content_preview: str = None) -> bool:
        """
        根据邮件标识符检查邮件是否已存在
        
        Args:
            email_id: 邮箱ID
            message_id: 邮件Message-ID
            server_uid: 服务器UID
            subject: 邮件主题
            sender: 发件人
            received_time: 接收时间
            content_preview: 内容预览（用于生成哈希）
            
        Returns:
            bool: 邮件是否已存在
        """
        try:
            # 生成mail_hash作为兜底检查
            mail_hash = None
            if subject and sender and received_time:
                mail_hash = generate_mail_hash(subject, sender, received_time, content_preview or "")
            
            return self.db.check_mail_exists(email_id, message_id, server_uid, mail_hash)
        except Exception as e:
            logger.error(f"检查邮件是否存在失败: {str(e)}")
            return False
    
    def batch_check_mails_exist(self, email_id: int, mail_list: List[Dict]) -> Set[Tuple[str, str]]:
        """
        批量检查邮件是否存在
        
        Args:
            email_id: 邮箱ID
            mail_list: 邮件列表，每个邮件包含message_id、server_uid等标识信息
            
        Returns:
            set: 已存在的邮件标识符集合
        """
        try:
            # 收集所有标识符
            mail_identifiers = {
                'message_ids': [],
                'server_uids': [],
                'mail_hashes': []
            }
            
            # 为每封邮件生成标识符
            for mail in mail_list:
                # 收集message_id
                message_id = mail.get('message_id')
                if message_id:
                    mail_identifiers['message_ids'].append(message_id)
                
                # 收集server_uid
                server_uid = mail.get('server_uid')
                if server_uid:
                    mail_identifiers['server_uids'].append(server_uid)
                
                # 生成mail_hash
                subject = mail.get('subject', '')
                sender = mail.get('sender', '')
                received_time = mail.get('received_time')
                content_preview = mail.get('content_preview', '')
                
                if subject and sender and received_time:
                    mail_hash = generate_mail_hash(subject, sender, received_time, content_preview)
                    mail_identifiers['mail_hashes'].append(mail_hash)
                    # 将生成的hash添加到邮件记录中，便于后续使用
                    mail['mail_hash'] = mail_hash
            
            # 批量查询数据库
            existing_identifiers = self.db.batch_check_mails_exist(email_id, mail_identifiers)
            
            logger.info(f"批量检查完成: 总邮件数={len(mail_list)}, 已存在={len(existing_identifiers)}")
            return existing_identifiers
            
        except Exception as e:
            logger.error(f"批量检查邮件是否存在失败: {str(e)}")
            return set()
    
    def filter_new_mails(self, email_id: int, mail_list: List[Dict]) -> List[Dict]:
        """
        过滤出新邮件（不存在于数据库中的邮件）
        
        Args:
            email_id: 邮箱ID
            mail_list: 邮件列表
            
        Returns:
            list: 新邮件列表
        """
        try:
            if not mail_list:
                return []
            
            # 批量检查邮件是否存在
            existing_identifiers = self.batch_check_mails_exist(email_id, mail_list)
            
            # 过滤出新邮件
            new_mails = []
            skipped_count = {'message_id': 0, 'server_uid': 0, 'mail_hash': 0}

            for mail in mail_list:
                is_existing = False
                skip_reason = None

                # 优先检查message_id
                message_id = mail.get('message_id')
                if message_id and ('message_id', message_id) in existing_identifiers:
                    is_existing = True
                    skip_reason = 'message_id'
                    skipped_count['message_id'] += 1

                # 其次检查server_uid
                if not is_existing:
                    server_uid = mail.get('server_uid')
                    if server_uid and ('server_uid', server_uid) in existing_identifiers:
                        is_existing = True
                        skip_reason = 'server_uid'
                        skipped_count['server_uid'] += 1

                # 最后检查mail_hash
                if not is_existing:
                    mail_hash = mail.get('mail_hash')
                    if mail_hash and ('mail_hash', mail_hash) in existing_identifiers:
                        is_existing = True
                        skip_reason = 'mail_hash'
                        skipped_count['mail_hash'] += 1

                if is_existing:
                    logger.debug(f"跳过已存在邮件: {mail.get('subject', '(无主题)')[:30]}... (匹配方式: {skip_reason})")
                else:
                    new_mails.append(mail)

            total_skipped = sum(skipped_count.values())
            logger.info(f"邮件过滤完成: 总数={len(mail_list)}, 新邮件={len(new_mails)}, 跳过={total_skipped}")
            logger.info(f"重复检测统计: Message-ID匹配={skipped_count['message_id']}, Server-UID匹配={skipped_count['server_uid']}, Hash匹配={skipped_count['mail_hash']}")
            return new_mails
            
        except Exception as e:
            logger.error(f"过滤新邮件失败: {str(e)}")
            return mail_list  # 出错时返回原列表，确保不丢失邮件
    
    def get_mail_identifiers_summary(self, mail_list: List[Dict]) -> Dict[str, int]:
        """
        获取邮件标识符统计信息
        
        Args:
            mail_list: 邮件列表
            
        Returns:
            dict: 标识符统计信息
        """
        summary = {
            'total_mails': len(mail_list),
            'with_message_id': 0,
            'with_server_uid': 0,
            'with_both_ids': 0,
            'with_no_ids': 0
        }
        
        for mail in mail_list:
            has_message_id = bool(mail.get('message_id'))
            has_server_uid = bool(mail.get('server_uid'))
            
            if has_message_id:
                summary['with_message_id'] += 1
            if has_server_uid:
                summary['with_server_uid'] += 1
            if has_message_id and has_server_uid:
                summary['with_both_ids'] += 1
            if not has_message_id and not has_server_uid:
                summary['with_no_ids'] += 1
        
        return summary

def create_mail_precheck_summary(mail_list: List[Dict]) -> str:
    """
    创建邮件预检查摘要信息
    
    Args:
        mail_list: 邮件列表
        
    Returns:
        str: 摘要信息
    """
    if not mail_list:
        return "无邮件需要检查"
    
    checker = MailPreChecker(None)  # 仅用于统计，不需要数据库连接
    summary = checker.get_mail_identifiers_summary(mail_list)
    
    return (f"邮件标识符统计: 总数={summary['total_mails']}, "
            f"有Message-ID={summary['with_message_id']}, "
            f"有Server-UID={summary['with_server_uid']}, "
            f"两者都有={summary['with_both_ids']}, "
            f"都没有={summary['with_no_ids']}")
