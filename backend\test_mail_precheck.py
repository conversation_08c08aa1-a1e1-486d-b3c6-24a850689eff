#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试邮件预检查机制
验证在fetch邮件之前能够正确判断邮件是否已存在，减少不必要的fetch操作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime, timedelta
from database.db import Database
from utils.email.mail_precheck import MailPreChecker, create_mail_precheck_summary
from utils.email.common import generate_mail_hash

def test_mail_precheck():
    """测试邮件预检查功能"""
    print("\n" + "=" * 60)
    print("测试邮件预检查机制")
    print("=" * 60)
    
    try:
        # 初始化数据库
        db = Database()
        precheck = MailPreChecker(db)
        
        # 测试数据
        test_email_id = 1
        
        # 准备测试邮件数据
        test_mails = [
            {
                'subject': '测试邮件1 - 预检查测试',
                'sender': '<EMAIL>',
                'received_time': datetime.now(),
                'message_id': '<EMAIL>',
                'server_uid': '12345',
                'content_preview': '这是测试邮件1的内容预览'
            },
            {
                'subject': '测试邮件2 - 预检查测试',
                'sender': '<EMAIL>', 
                'received_time': datetime.now() - timedelta(hours=1),
                'message_id': '<EMAIL>',
                'server_uid': '12346',
                'content_preview': '这是测试邮件2的内容预览'
            },
            {
                'subject': '测试邮件3 - 无标识符',
                'sender': '<EMAIL>',
                'received_time': datetime.now() - timedelta(hours=2),
                'content_preview': '这是测试邮件3的内容预览'
            }
        ]
        
        print(f"准备测试邮件: {len(test_mails)} 封")
        
        # 显示邮件标识符统计
        summary = create_mail_precheck_summary(test_mails)
        print(f"邮件标识符统计: {summary}")
        
        # 1. 测试单个邮件检查（邮件不存在）
        print("\n1. 测试单个邮件检查（邮件不存在）")
        mail1 = test_mails[0]
        exists = precheck.check_mail_exists_by_identifiers(
            email_id=test_email_id,
            message_id=mail1['message_id'],
            server_uid=mail1['server_uid'],
            subject=mail1['subject'],
            sender=mail1['sender'],
            received_time=mail1['received_time'],
            content_preview=mail1['content_preview']
        )
        print(f"邮件1是否存在: {exists} (预期: False)")
        
        # 2. 添加一封邮件到数据库
        print("\n2. 添加邮件到数据库")
        success, mail_id = db.add_mail_record(
            email_id=test_email_id,
            subject=mail1['subject'],
            sender=mail1['sender'],
            received_time=mail1['received_time'],
            content=mail1['content_preview'],
            message_id=mail1['message_id'],
            server_uid=mail1['server_uid']
        )
        print(f"邮件添加结果: success={success}, mail_id={mail_id}")
        
        # 3. 再次检查该邮件（应该存在）
        print("\n3. 再次检查该邮件（应该存在）")
        exists = precheck.check_mail_exists_by_identifiers(
            email_id=test_email_id,
            message_id=mail1['message_id'],
            server_uid=mail1['server_uid'],
            subject=mail1['subject'],
            sender=mail1['sender'],
            received_time=mail1['received_time'],
            content_preview=mail1['content_preview']
        )
        print(f"邮件1是否存在: {exists} (预期: True)")
        
        # 4. 测试批量检查
        print("\n4. 测试批量检查")
        existing_identifiers = precheck.batch_check_mails_exist(test_email_id, test_mails)
        print(f"已存在的标识符: {existing_identifiers}")
        
        # 5. 测试过滤新邮件
        print("\n5. 测试过滤新邮件")
        new_mails = precheck.filter_new_mails(test_email_id, test_mails)
        print(f"过滤结果: 原始邮件数={len(test_mails)}, 新邮件数={len(new_mails)}")
        for i, mail in enumerate(new_mails):
            print(f"  新邮件{i+1}: {mail['subject']}")
        
        # 6. 测试数据库批量检查方法
        print("\n6. 测试数据库批量检查方法")
        mail_identifiers = {
            'message_ids': [mail.get('message_id') for mail in test_mails if mail.get('message_id')],
            'server_uids': [mail.get('server_uid') for mail in test_mails if mail.get('server_uid')],
            'mail_hashes': []
        }
        
        # 生成mail_hash
        for mail in test_mails:
            if mail.get('subject') and mail.get('sender') and mail.get('received_time'):
                mail_hash = generate_mail_hash(
                    mail['subject'], 
                    mail['sender'], 
                    mail['received_time'], 
                    mail.get('content_preview', '')
                )
                mail_identifiers['mail_hashes'].append(mail_hash)
        
        print(f"查询标识符: {mail_identifiers}")
        existing_db = db.batch_check_mails_exist(test_email_id, mail_identifiers)
        print(f"数据库中已存在的标识符: {existing_db}")
        
        print("\n" + "=" * 60)
        print("邮件预检查测试完成")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_performance():
    """测试预检查性能"""
    print("\n" + "=" * 60)
    print("测试预检查性能")
    print("=" * 60)
    
    try:
        import time
        db = Database()
        precheck = MailPreChecker(db)
        
        # 生成大量测试邮件
        test_mails = []
        for i in range(100):
            test_mails.append({
                'subject': f'性能测试邮件 {i}',
                'sender': f'test{i}@example.com',
                'received_time': datetime.now() - timedelta(minutes=i),
                'message_id': f'perf-test-{i}@example.com',
                'server_uid': str(10000 + i),
                'content_preview': f'这是性能测试邮件{i}的内容'
            })
        
        print(f"生成测试邮件: {len(test_mails)} 封")
        
        # 测试批量检查性能
        start_time = time.time()
        existing_identifiers = precheck.batch_check_mails_exist(1, test_mails)
        end_time = time.time()
        
        print(f"批量检查耗时: {end_time - start_time:.3f} 秒")
        print(f"已存在邮件数: {len(existing_identifiers)}")
        print(f"平均每封邮件检查耗时: {(end_time - start_time) / len(test_mails) * 1000:.2f} 毫秒")
        
        return True
        
    except Exception as e:
        print(f"性能测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("开始测试邮件预检查机制...")
    
    # 基本功能测试
    success1 = test_mail_precheck()
    
    # 性能测试
    success2 = test_performance()
    
    if success1 and success2:
        print("\n✅ 所有测试通过")
    else:
        print("\n❌ 部分测试失败")
        sys.exit(1)
