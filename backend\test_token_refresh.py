#!/usr/bin/env python3
"""
测试refresh_token更新功能
"""

import sys
import os
sys.path.append('.')

from utils.email.outlook import OutlookMailHandler
from database.db import Database
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_token_refresh_functionality():
    """测试token刷新功能"""
    print("=" * 60)
    print("测试refresh_token更新功能")
    print("=" * 60)
    
    # 1. 测试get_new_access_token方法返回格式
    print("\n1. 测试get_new_access_token方法返回格式...")
    try:
        # 使用无效token测试返回格式
        result = OutlookMailHandler.get_new_access_token('invalid_token', 'invalid_client_id')
        print(f"   返回结果类型: {type(result)}")
        print(f"   返回结果: {result}")
        
        if isinstance(result, tuple) and len(result) == 2:
            print("   ✓ 方法正确返回两个值 (access_token, refresh_token)")
        else:
            print("   ✗ 方法返回格式不正确")
            return False
    except Exception as e:
        print(f"   ✗ 测试过程中出现异常: {e}")
        return False
    
    # 2. 测试数据库方法
    print("\n2. 测试数据库token更新方法...")
    try:
        db = Database()
        
        # 检查方法是否存在
        methods_to_check = [
            'update_email_tokens',
            'update_email_refresh_token',
            'update_email_token'
        ]
        
        for method_name in methods_to_check:
            if hasattr(db, method_name):
                print(f"   ✓ {method_name}方法存在")
            else:
                print(f"   ✗ {method_name}方法不存在")
                return False
                
    except Exception as e:
        print(f"   ✗ 测试数据库方法时出现异常: {e}")
        return False
    
    # 3. 测试方法调用兼容性
    print("\n3. 测试方法调用兼容性...")
    try:
        # 模拟旧的调用方式，看是否会出错
        print("   测试旧代码兼容性...")
        
        # 这里我们不能真正调用，因为需要有效的token
        # 但我们可以检查方法签名
        import inspect
        
        # 检查get_new_access_token的签名
        sig = inspect.signature(OutlookMailHandler.get_new_access_token)
        params = list(sig.parameters.keys())
        print(f"   get_new_access_token参数: {params}")
        
        if 'refresh_token' in params and 'client_id' in params:
            print("   ✓ get_new_access_token方法签名正确")
        else:
            print("   ✗ get_new_access_token方法签名不正确")
            return False
            
    except Exception as e:
        print(f"   ✗ 测试方法兼容性时出现异常: {e}")
        return False
    
    print("\n" + "=" * 60)
    print("✓ 所有测试通过！refresh_token更新功能已正确实现")
    print("=" * 60)
    
    print("\n功能说明:")
    print("1. get_new_access_token现在返回(access_token, new_refresh_token)")
    print("2. 数据库新增update_email_tokens方法，可同时更新两个token")
    print("3. 数据库新增update_email_refresh_token方法，可单独更新refresh_token")
    print("4. 所有邮件检查逻辑已更新，会自动更新新的refresh_token")
    
    return True

if __name__ == "__main__":
    success = test_token_refresh_functionality()
    if success:
        print("\n🎉 测试成功！")
        sys.exit(0)
    else:
        print("\n❌ 测试失败！")
        sys.exit(1)
