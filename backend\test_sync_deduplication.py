#!/usr/bin/env python3
"""
测试邮件同步去重功能
验证改进后的同步机制是否能有效避免重复同步当天邮件
"""

import sys
import os
sys.path.append('.')

from database.db import Database
from utils.email.mail_processor import MailProcessor
from datetime import datetime, timezone, timedelta
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_mock_mail_records():
    """创建模拟的邮件记录"""
    china_tz = timezone(timedelta(hours=8))
    current_time = datetime.now(china_tz)
    
    # 模拟今天收到的邮件
    mock_emails = [
        {
            'subject': '重要通知：系统维护计划',
            'sender': '<EMAIL>',
            'content': '系统将于今晚22:00-24:00进行维护，请提前保存工作。',
            'message_id': '<EMAIL>',
            'server_uid': '2001',
            'received_time': current_time,
            'folder': 'INBOX'
        },
        {
            'subject': '会议邀请：项目进度评审',
            'sender': '<EMAIL>',
            'content': '请参加明天下午2点的项目进度评审会议。',
            'message_id': '<EMAIL>',
            'server_uid': '2002',
            'received_time': current_time,
            'folder': 'INBOX'
        },
        {
            'subject': '新员工入职通知',
            'sender': '<EMAIL>',
            'content': '欢迎新员工张三加入我们团队。',
            'message_id': '<EMAIL>',
            'server_uid': '2003',
            'received_time': current_time,
            'folder': 'INBOX'
        }
    ]
    
    return mock_emails

def test_first_sync():
    """测试第一次同步"""
    print("=" * 60)
    print("测试第一次邮件同步")
    print("=" * 60)
    
    try:
        db = Database()
        processor = MailProcessor()
        
        # 模拟邮箱ID
        email_id = 1
        
        # 获取模拟邮件
        mock_emails = create_mock_mail_records()
        
        print(f"模拟获取到 {len(mock_emails)} 封邮件:")
        for i, email in enumerate(mock_emails, 1):
            print(f"  {i}. {email['subject']}")
            print(f"     Message-ID: {email['message_id']}")
            print(f"     Server-UID: {email['server_uid']}")
        
        # 第一次同步
        print(f"\n开始第一次同步...")
        saved_count = processor.save_mail_records(db, email_id, mock_emails)
        
        print(f"第一次同步结果: 保存了 {saved_count} 封邮件")
        
        # 验证数据库中的记录
        cursor = db.conn.execute("""
            SELECT subject, message_id, server_uid 
            FROM mail_records 
            WHERE email_id = ? 
            ORDER BY id DESC 
            LIMIT ?
        """, (email_id, len(mock_emails)))
        
        saved_records = cursor.fetchall()
        print(f"数据库中的记录数: {len(saved_records)}")
        
        for record in saved_records:
            print(f"  - {record[0][:30]}... (ID: {record[1][:20]}...)")
        
        return saved_count == len(mock_emails)
        
    except Exception as e:
        print(f"第一次同步测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_second_sync():
    """测试第二次同步（应该检测到重复）"""
    print("\n" + "=" * 60)
    print("测试第二次邮件同步（重复检测）")
    print("=" * 60)
    
    try:
        db = Database()
        processor = MailProcessor()
        
        # 模拟邮箱ID
        email_id = 1
        
        # 获取相同的模拟邮件（模拟重复同步）
        mock_emails = create_mock_mail_records()
        
        print(f"再次获取到 {len(mock_emails)} 封邮件（相同的邮件）:")
        for i, email in enumerate(mock_emails, 1):
            print(f"  {i}. {email['subject']}")
            print(f"     Message-ID: {email['message_id']}")
        
        # 第二次同步
        print(f"\n开始第二次同步...")
        saved_count = processor.save_mail_records(db, email_id, mock_emails)
        
        print(f"第二次同步结果: 保存了 {saved_count} 封邮件")
        
        if saved_count == 0:
            print("✓ 重复检测工作正常，没有保存重复邮件")
            return True
        else:
            print(f"✗ 重复检测失败，保存了 {saved_count} 封重复邮件")
            return False
        
    except Exception as e:
        print(f"第二次同步测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_partial_duplicate_sync():
    """测试部分重复的同步（新邮件+重复邮件）"""
    print("\n" + "=" * 60)
    print("测试部分重复同步（新邮件+重复邮件）")
    print("=" * 60)
    
    try:
        db = Database()
        processor = MailProcessor()
        
        # 模拟邮箱ID
        email_id = 1
        
        # 获取原有邮件 + 新邮件
        original_emails = create_mock_mail_records()
        china_tz = timezone(timedelta(hours=8))
        current_time = datetime.now(china_tz)
        
        # 添加新邮件
        new_emails = [
            {
                'subject': '紧急通知：服务器故障',
                'sender': '<EMAIL>',
                'content': '服务器出现故障，正在紧急修复中。',
                'message_id': '<EMAIL>',
                'server_uid': '2004',
                'received_time': current_time,
                'folder': 'INBOX'
            },
            {
                'subject': '周报提醒',
                'sender': '<EMAIL>',
                'content': '请记得提交本周的工作周报。',
                'message_id': '<EMAIL>',
                'server_uid': '2005',
                'received_time': current_time,
                'folder': 'INBOX'
            }
        ]
        
        # 混合邮件：原有邮件 + 新邮件
        mixed_emails = original_emails + new_emails
        
        print(f"获取到 {len(mixed_emails)} 封邮件（{len(original_emails)} 封重复 + {len(new_emails)} 封新邮件）:")
        for i, email in enumerate(mixed_emails, 1):
            is_new = i > len(original_emails)
            status = "[新]" if is_new else "[重复]"
            print(f"  {i}. {status} {email['subject']}")
        
        # 第三次同步
        print(f"\n开始第三次同步...")
        saved_count = processor.save_mail_records(db, email_id, mixed_emails)
        
        print(f"第三次同步结果: 保存了 {saved_count} 封邮件")
        
        if saved_count == len(new_emails):
            print(f"✓ 部分重复检测工作正常，只保存了 {len(new_emails)} 封新邮件")
            return True
        else:
            print(f"✗ 部分重复检测失败，期望保存 {len(new_emails)} 封，实际保存 {saved_count} 封")
            return False
        
    except Exception as e:
        print(f"部分重复同步测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def cleanup_test_data():
    """清理测试数据"""
    print("\n" + "=" * 60)
    print("清理测试数据")
    print("=" * 60)
    
    try:
        db = Database()
        
        # 删除测试邮件
        test_message_ids = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ]
        
        for message_id in test_message_ids:
            db.conn.execute("DELETE FROM mail_records WHERE message_id = ?", (message_id,))
        
        db.conn.commit()
        print(f"✓ 已清理 {len(test_message_ids)} 条测试记录")
        return True
        
    except Exception as e:
        print(f"清理测试数据失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试邮件同步去重功能...\n")
    
    tests = [
        ("第一次同步", test_first_sync),
        ("第二次同步（重复检测）", test_second_sync),
        ("部分重复同步", test_partial_duplicate_sync),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"测试 {test_name} 出现异常: {e}")
            results.append((test_name, False))
    
    # 清理测试数据
    cleanup_test_data()
    
    # 总结
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！邮件同步去重功能正常工作")
        print("\n改进效果:")
        print("✅ 避免重复同步当天邮件")
        print("✅ 基于Message-ID精确去重")
        print("✅ 支持部分重复场景（只同步新邮件）")
        print("✅ 提高同步效率，减少存储浪费")
        return True
    else:
        print(f"\n❌ {total - passed} 个测试失败，需要修复问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
