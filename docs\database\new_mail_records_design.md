# 邮件记录表优化方案（修订版）

## 设计目标

1. ~~删除不需要的自增id字段~~ **保留id字段以维持兼容性**
2. 使用邮件的唯一标识来判断重复，减少fetch次数
3. 保证邮件记录的唯一性和完整性
4. 保持与附件表的关联关系
5. 实现高效的邮件预检查机制

## 当前问题分析

### 现有表结构问题
- `id`字段作为自增主键，但实际上邮件的唯一性应该由邮件本身的标识决定
- 重复检查逻辑复杂，需要多次查询数据库
- 无法在fetch之前有效判断邮件是否已存在

### 邮件唯一标识分析
1. **message_id**: 邮件的Message-ID头，最可靠的唯一标识，但可能为空
2. **server_uid**: 服务器端的UID，对于IMAP等协议比较可靠，但可能为空
3. **mail_hash**: 基于邮件内容生成的哈希值，作为兜底方案

## 优化方案：保留id字段 + 增强唯一标识

### 实际采用的表结构（兼容性优先）

```sql
CREATE TABLE IF NOT EXISTS mail_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,  -- 保留用于外键引用和API兼容
    email_id INTEGER NOT NULL,
    subject TEXT,
    sender TEXT,
    received_time TIMESTAMP,
    content TEXT,
    folder TEXT,
    has_attachments INTEGER DEFAULT 0,
    message_id TEXT,     -- 邮件Message-ID，优先用于去重
    server_uid TEXT,     -- 服务器UID，次优去重标识
    mail_hash TEXT,      -- 内容哈希，兜底去重方案
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (email_id) REFERENCES emails (id)
);
```

### 优化策略

1. **保留id字段的原因**：
   - 维持与attachments表的外键关系
   - 保持现有API的兼容性
   - 避免复杂的数据迁移

2. **增强去重机制**：
   - 使用message_id、server_uid、mail_hash进行多层去重
   - 实现预检查机制，在fetch前判断邮件是否存在
   - 提高去重的准确性和效率

### 优势
1. **明确的语义**: 每个字段都有明确的含义
2. **灵活性**: 可以根据不同的标识符类型进行查询
3. **数据完整性**: 复合主键确保唯一性
4. **向后兼容**: 保留原始字段，便于迁移和查询

### 邮件标识符优先级
1. 优先使用 `message_id` (identifier_type = 'message_id')
2. 其次使用 `server_uid` (identifier_type = 'server_uid')  
3. 最后使用 `mail_hash` (identifier_type = 'mail_hash')

### 附件表修改

```sql
CREATE TABLE IF NOT EXISTS attachments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    email_id INTEGER NOT NULL,
    mail_identifier TEXT NOT NULL,
    identifier_type TEXT NOT NULL,
    filename TEXT,
    content_type TEXT,
    size INTEGER,
    content BLOB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (email_id, mail_identifier, identifier_type) 
        REFERENCES mail_records (email_id, mail_identifier, identifier_type) 
        ON DELETE CASCADE
);
```

## 邮件预检查机制

### 检查逻辑
```python
def check_mail_exists(email_id, message_id=None, server_uid=None, mail_hash=None):
    """检查邮件是否已存在，按优先级检查"""
    
    # 1. 优先检查 message_id
    if message_id:
        cursor = conn.execute(
            "SELECT 1 FROM mail_records WHERE email_id = ? AND mail_identifier = ? AND identifier_type = 'message_id'",
            (email_id, message_id)
        )
        if cursor.fetchone():
            return True
    
    # 2. 检查 server_uid
    if server_uid:
        cursor = conn.execute(
            "SELECT 1 FROM mail_records WHERE email_id = ? AND mail_identifier = ? AND identifier_type = 'server_uid'",
            (email_id, server_uid)
        )
        if cursor.fetchone():
            return True
    
    # 3. 检查 mail_hash
    if mail_hash:
        cursor = conn.execute(
            "SELECT 1 FROM mail_records WHERE email_id = ? AND mail_identifier = ? AND identifier_type = 'mail_hash'",
            (email_id, mail_hash)
        )
        if cursor.fetchone():
            return True
    
    return False
```

### 批量预检查
```python
def batch_check_mails_exist(email_id, mail_identifiers):
    """批量检查邮件是否存在"""
    existing_identifiers = set()
    
    # 构建查询条件
    conditions = []
    params = []
    
    for identifier_type, identifiers in mail_identifiers.items():
        if identifiers:
            placeholders = ','.join(['?' for _ in identifiers])
            conditions.append(f"(identifier_type = ? AND mail_identifier IN ({placeholders}))")
            params.extend([identifier_type] + list(identifiers))
    
    if not conditions:
        return existing_identifiers
    
    query = f"""
        SELECT mail_identifier, identifier_type 
        FROM mail_records 
        WHERE email_id = ? AND ({' OR '.join(conditions)})
    """
    
    cursor = conn.execute(query, [email_id] + params)
    for row in cursor.fetchall():
        existing_identifiers.add((row['identifier_type'], row['mail_identifier']))
    
    return existing_identifiers
```

## 迁移策略

1. **创建新表结构**
2. **数据迁移**: 将现有数据按优先级迁移到新表
3. **更新代码**: 修改相关的数据库操作方法
4. **测试验证**: 确保功能正常
5. **删除旧表**: 完成迁移后删除旧表

## 性能优化

### 索引设计
```sql
-- 主键索引（自动创建）
-- PRIMARY KEY (email_id, mail_identifier, identifier_type)

-- 查询优化索引
CREATE INDEX idx_mail_records_email_id ON mail_records(email_id);
CREATE INDEX idx_mail_records_message_id ON mail_records(message_id) WHERE message_id IS NOT NULL;
CREATE INDEX idx_mail_records_server_uid ON mail_records(server_uid) WHERE server_uid IS NOT NULL;
CREATE INDEX idx_mail_records_mail_hash ON mail_records(mail_hash) WHERE mail_hash IS NOT NULL;
CREATE INDEX idx_mail_records_received_time ON mail_records(received_time);
```

这个设计方案可以有效解决当前的问题，提高邮件去重效率，减少不必要的fetch操作。
