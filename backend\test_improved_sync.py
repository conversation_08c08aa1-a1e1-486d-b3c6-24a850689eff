#!/usr/bin/env python3
"""
测试改进后的邮件同步机制
"""

import sys
import os
sys.path.append('.')

from database.db import Database
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_database_schema():
    """测试数据库schema是否包含新字段"""
    print("=" * 60)
    print("测试数据库schema")
    print("=" * 60)
    
    try:
        db = Database()
        
        # 检查mail_records表的字段
        cursor = db.conn.execute("PRAGMA table_info(mail_records)")
        columns = cursor.fetchall()
        
        print("mail_records表的字段:")
        column_names = []
        for column in columns:
            column_names.append(column[1])
            print(f"  - {column[1]} ({column[2]})")
        
        # 检查是否包含新字段
        required_fields = ['message_id', 'server_uid', 'mail_hash']
        missing_fields = []
        
        for field in required_fields:
            if field in column_names:
                print(f"  ✓ {field} 字段存在")
            else:
                print(f"  ✗ {field} 字段缺失")
                missing_fields.append(field)
        
        if missing_fields:
            print(f"\n缺失字段: {missing_fields}")
            return False
        else:
            print("\n✓ 所有必需字段都存在")
            return True
            
    except Exception as e:
        print(f"✗ 测试数据库schema失败: {e}")
        return False

def test_add_mail_record_with_ids():
    """测试使用唯一ID添加邮件记录"""
    print("\n" + "=" * 60)
    print("测试邮件记录添加（带唯一ID）")
    print("=" * 60)
    
    try:
        db = Database()
        
        # 测试数据
        test_email_id = 1  # 假设存在邮箱ID为1
        test_subject = "测试邮件 - 唯一ID测试"
        test_sender = "<EMAIL>"
        test_content = "这是一封测试邮件内容"
        test_message_id = "<EMAIL>"
        test_server_uid = "12345"
        
        print(f"测试添加邮件:")
        print(f"  主题: {test_subject}")
        print(f"  Message-ID: {test_message_id}")
        print(f"  Server-UID: {test_server_uid}")
        
        # 第一次添加
        success1, mail_id1 = db.add_mail_record(
            email_id=test_email_id,
            subject=test_subject,
            sender=test_sender,
            received_time=None,
            content=test_content,
            message_id=test_message_id,
            server_uid=test_server_uid
        )
        
        print(f"\n第一次添加结果: success={success1}, mail_id={mail_id1}")
        
        # 第二次添加相同的message_id（应该被拒绝）
        success2, mail_id2 = db.add_mail_record(
            email_id=test_email_id,
            subject=test_subject + " (重复)",
            sender=test_sender,
            received_time=None,
            content=test_content + " (重复)",
            message_id=test_message_id,  # 相同的message_id
            server_uid="67890"  # 不同的server_uid
        )
        
        print(f"第二次添加结果: success={success2}, mail_id={mail_id2}")
        
        # 第三次添加相同的server_uid（应该被拒绝）
        success3, mail_id3 = db.add_mail_record(
            email_id=test_email_id,
            subject=test_subject + " (重复2)",
            sender=test_sender,
            received_time=None,
            content=test_content + " (重复2)",
            message_id="<EMAIL>",
            server_uid=test_server_uid  # 相同的server_uid
        )
        
        print(f"第三次添加结果: success={success3}, mail_id={mail_id3}")
        
        # 验证结果
        if success1 and not success2 and not success3:
            print("\n✓ 唯一ID去重机制工作正常")
            print("  - 第一次添加成功")
            print("  - 重复message_id被正确拒绝")
            print("  - 重复server_uid被正确拒绝")
            
            # 清理测试数据
            if mail_id1:
                db.conn.execute("DELETE FROM mail_records WHERE id = ?", (mail_id1,))
                db.conn.commit()
                print("  - 测试数据已清理")
            
            return True
        else:
            print("\n✗ 唯一ID去重机制存在问题")
            return False
            
    except Exception as e:
        print(f"✗ 测试邮件记录添加失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mail_record_method_signature():
    """测试add_mail_record方法签名"""
    print("\n" + "=" * 60)
    print("测试add_mail_record方法签名")
    print("=" * 60)
    
    try:
        import inspect
        from database.db import Database
        
        # 检查方法签名
        sig = inspect.signature(Database.add_mail_record)
        params = list(sig.parameters.keys())
        
        print("add_mail_record方法参数:")
        for param in params:
            print(f"  - {param}")
        
        # 检查是否包含新参数
        required_params = ['message_id', 'server_uid']
        missing_params = []
        
        for param in required_params:
            if param in params:
                print(f"  ✓ {param} 参数存在")
            else:
                print(f"  ✗ {param} 参数缺失")
                missing_params.append(param)
        
        if missing_params:
            print(f"\n缺失参数: {missing_params}")
            return False
        else:
            print("\n✓ 方法签名正确")
            return True
            
    except Exception as e:
        print(f"✗ 测试方法签名失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试改进后的邮件同步机制...")
    
    tests = [
        ("数据库Schema", test_database_schema),
        ("方法签名", test_mail_record_method_signature),
        ("唯一ID去重", test_add_mail_record_with_ids),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"测试 {test_name} 出现异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！改进后的邮件同步机制已正确实现")
        print("\n功能说明:")
        print("1. 数据库新增message_id和server_uid字段用于唯一标识")
        print("2. add_mail_record方法优先使用message_id和server_uid进行去重")
        print("3. 邮件获取逻辑已更新，会提取并传递唯一标识")
        print("4. 这将有效避免重复同步当天的邮件")
        return True
    else:
        print(f"\n❌ {total - passed} 个测试失败，需要修复问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
