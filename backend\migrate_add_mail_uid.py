#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def migrate_add_mail_uid():
    """添加邮件UID字段到数据库"""
    try:
        db_path = os.path.join('data', 'huohuo_email.db')
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("开始添加邮件UID字段...")
        
        # 1. 检查是否已经有mail_uid字段
        cursor.execute('PRAGMA table_info(mail_records)')
        columns = [col[1] for col in cursor.fetchall()]
        
        if 'mail_uid' not in columns:
            print("添加mail_uid字段...")
            cursor.execute('ALTER TABLE mail_records ADD COLUMN mail_uid TEXT')
            conn.commit()
            print("mail_uid字段添加成功")
        else:
            print("mail_uid字段已存在")
        
        # 2. 创建复合唯一索引（email_id + mail_uid）
        cursor.execute("SELECT name FROM sqlite_master WHERE type='index' AND name='idx_email_mail_uid_unique'")
        if not cursor.fetchone():
            print("创建邮件UID唯一索引...")
            cursor.execute('CREATE UNIQUE INDEX idx_email_mail_uid_unique ON mail_records(email_id, mail_uid) WHERE mail_uid IS NOT NULL')
            conn.commit()
            print("邮件UID唯一索引创建成功")
        else:
            print("邮件UID唯一索引已存在")
        
        # 3. 创建普通索引用于快速查询
        cursor.execute("SELECT name FROM sqlite_master WHERE type='index' AND name='idx_mail_uid'")
        if not cursor.fetchone():
            print("创建邮件UID查询索引...")
            cursor.execute('CREATE INDEX idx_mail_uid ON mail_records(mail_uid)')
            conn.commit()
            print("邮件UID查询索引创建成功")
        else:
            print("邮件UID查询索引已存在")
        
        # 4. 验证结果
        cursor.execute('PRAGMA table_info(mail_records)')
        columns = cursor.fetchall()
        
        print("\n更新后的mail_records表结构:")
        print('-' * 60)
        for col in columns:
            cid, name, type_, notnull, default, pk = col
            nullable = "NOT NULL" if notnull else "NULL"
            primary = "PRIMARY KEY" if pk else ""
            print(f"{name:20} {type_:15} {nullable:10} {primary}")
        
        # 5. 查看索引
        cursor.execute("SELECT name, sql FROM sqlite_master WHERE type='index' AND tbl_name='mail_records'")
        indexes = cursor.fetchall()
        
        print('\nmail_records表索引:')
        print('-' * 60)
        for idx in indexes:
            if idx[1]:  # 排除自动创建的索引
                print(f"索引名: {idx[0]}")
                print(f"SQL: {idx[1]}")
                print()
        
        conn.close()
        print("邮件UID字段迁移完成！")
        
    except Exception as e:
        print(f"迁移失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    migrate_add_mail_uid()
