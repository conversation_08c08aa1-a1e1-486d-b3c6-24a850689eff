#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试带预检查的邮件获取功能
验证在实际邮件获取过程中，预检查机制能够有效减少重复fetch
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime
from database.db import Database
from utils.email.mail_processor import EmailBatchProcessor

def test_fetch_with_precheck():
    """测试带预检查的邮件获取"""
    print("\n" + "=" * 60)
    print("测试带预检查的邮件获取功能")
    print("=" * 60)
    
    try:
        # 初始化数据库和处理器
        db = Database()
        processor = EmailBatchProcessor(db)
        
        # 获取一个测试邮箱账户
        users = db.get_all_users()
        if not users:
            print("❌ 没有找到用户，请先创建用户和邮箱账户")
            return False
        
        user = users[0]
        emails = db.get_user_emails(user['id'])
        if not emails:
            print("❌ 没有找到邮箱账户，请先添加邮箱")
            return False
        
        email_account = emails[0]
        email_id = email_account['id']
        
        print(f"使用邮箱: {email_account['email']} (ID: {email_id})")
        print(f"邮箱类型: {email_account.get('mail_type', 'unknown')}")
        
        # 获取当前邮件数量
        existing_mails = db.get_mail_records(email_id)
        existing_count = len(existing_mails)
        print(f"数据库中现有邮件数: {existing_count}")
        
        # 创建进度回调
        def progress_callback(progress, message):
            print(f"进度: {progress}% - {message}")
        
        # 第一次获取邮件（可能会有新邮件）
        print("\n第一次获取邮件...")
        start_time = datetime.now()
        
        result = processor._check_email_task(email_account, progress_callback)
        
        end_time = datetime.now()
        duration1 = (end_time - start_time).total_seconds()
        
        print(f"第一次获取结果: {result}")
        print(f"第一次获取耗时: {duration1:.2f} 秒")
        
        # 获取第一次获取后的邮件数量
        after_first_mails = db.get_mail_records(email_id)
        after_first_count = len(after_first_mails)
        new_mails_first = after_first_count - existing_count
        
        print(f"第一次获取后邮件数: {after_first_count}")
        print(f"第一次新增邮件数: {new_mails_first}")
        
        # 立即进行第二次获取（应该没有新邮件，预检查应该跳过大部分邮件）
        print("\n第二次获取邮件（测试预检查效果）...")
        start_time = datetime.now()
        
        result2 = processor._check_email_task(email_account, progress_callback)
        
        end_time = datetime.now()
        duration2 = (end_time - start_time).total_seconds()
        
        print(f"第二次获取结果: {result2}")
        print(f"第二次获取耗时: {duration2:.2f} 秒")
        
        # 获取第二次获取后的邮件数量
        after_second_mails = db.get_mail_records(email_id)
        after_second_count = len(after_second_mails)
        new_mails_second = after_second_count - after_first_count
        
        print(f"第二次获取后邮件数: {after_second_count}")
        print(f"第二次新增邮件数: {new_mails_second}")
        
        # 分析预检查效果
        print("\n" + "=" * 40)
        print("预检查效果分析")
        print("=" * 40)
        
        if duration2 < duration1:
            speedup = duration1 / duration2 if duration2 > 0 else float('inf')
            print(f"✅ 第二次获取更快: {speedup:.2f}x 倍速提升")
        else:
            print(f"⚠️  第二次获取耗时相近或更长")
        
        if new_mails_second == 0:
            print("✅ 第二次获取没有重复邮件，预检查工作正常")
        else:
            print(f"⚠️  第二次获取仍有 {new_mails_second} 封新邮件")
        
        print(f"时间对比: 第一次 {duration1:.2f}s vs 第二次 {duration2:.2f}s")
        
        # 测试特定邮件的预检查
        if after_first_count > 0:
            print("\n测试特定邮件的预检查...")
            sample_mail = after_first_mails[0]
            
            # 检查这封邮件是否能被正确识别为已存在
            exists = db.check_mail_exists(
                email_id,
                sample_mail.get('message_id'),
                sample_mail.get('server_uid'),
                sample_mail.get('mail_hash')
            )
            print(f"样本邮件预检查结果: {exists} (预期: True)")
            print(f"样本邮件信息: 主题='{sample_mail.get('subject', '(无主题)')[:50]}...'")
            print(f"  Message-ID: {sample_mail.get('message_id', '(无)')}")
            print(f"  Server-UID: {sample_mail.get('server_uid', '(无)')}")
            print(f"  Mail-Hash: {sample_mail.get('mail_hash', '(无)')[:16]}...")
        
        print("\n" + "=" * 60)
        print("带预检查的邮件获取测试完成")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_precheck_statistics():
    """测试预检查统计信息"""
    print("\n" + "=" * 60)
    print("测试预检查统计信息")
    print("=" * 60)
    
    try:
        db = Database()
        
        # 获取所有邮箱的邮件统计
        users = db.get_all_users()
        total_mails = 0
        total_with_message_id = 0
        total_with_server_uid = 0
        total_with_mail_hash = 0
        
        for user in users:
            emails = db.get_user_emails(user['id'])
            for email_account in emails:
                mails = db.get_mail_records(email_account['id'])
                total_mails += len(mails)
                
                for mail in mails:
                    if mail.get('message_id'):
                        total_with_message_id += 1
                    if mail.get('server_uid'):
                        total_with_server_uid += 1
                    if mail.get('mail_hash'):
                        total_with_mail_hash += 1
        
        print(f"数据库邮件统计:")
        print(f"  总邮件数: {total_mails}")
        print(f"  有Message-ID: {total_with_message_id} ({total_with_message_id/total_mails*100:.1f}%)" if total_mails > 0 else "  有Message-ID: 0")
        print(f"  有Server-UID: {total_with_server_uid} ({total_with_server_uid/total_mails*100:.1f}%)" if total_mails > 0 else "  有Server-UID: 0")
        print(f"  有Mail-Hash: {total_with_mail_hash} ({total_with_mail_hash/total_mails*100:.1f}%)" if total_mails > 0 else "  有Mail-Hash: 0")
        
        # 预检查覆盖率分析
        if total_mails > 0:
            coverage = max(total_with_message_id, total_with_server_uid, total_with_mail_hash) / total_mails * 100
            print(f"  预检查覆盖率: {coverage:.1f}%")
            
            if coverage >= 90:
                print("✅ 预检查覆盖率良好")
            elif coverage >= 70:
                print("⚠️  预检查覆盖率一般")
            else:
                print("❌ 预检查覆盖率较低")
        
        return True
        
    except Exception as e:
        print(f"统计测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("开始测试带预检查的邮件获取功能...")
    
    # 预检查统计
    success1 = test_precheck_statistics()
    
    # 实际获取测试
    success2 = test_fetch_with_precheck()
    
    if success1 and success2:
        print("\n✅ 所有测试通过")
    else:
        print("\n❌ 部分测试失败")
        sys.exit(1)
