#!/usr/bin/env python3
"""
测试优化后的邮件同步机制
验证Message-ID和Server-UID的重复检测性能和准确性
"""

import sys
import os
import time
import random
import string
from datetime import datetime, timedelta

sys.path.append('.')

from database.db import Database
from utils.email.mail_precheck import <PERSON><PERSON><PERSON><PERSON>he<PERSON>
from utils.email.common import generate_mail_hash, get_china_now
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def generate_test_mail(index, base_time=None):
    """生成测试邮件数据"""
    if base_time is None:
        base_time = get_china_now()
    
    # 生成随机的Message-ID
    random_id = ''.join(random.choices(string.ascii_letters + string.digits, k=20))
    message_id = f"{random_id}@test.example.com"
    
    # 生成随机的Server-UID
    server_uid = str(random.randint(10000, 99999))
    
    # 生成邮件内容
    subject = f"测试邮件 #{index}"
    sender = f"sender{index}@test.com"
    received_time = base_time + timedelta(minutes=index)
    content = f"这是第{index}封测试邮件的内容"
    
    return {
        'message_id': message_id,
        'server_uid': server_uid,
        'subject': subject,
        'sender': sender,
        'received_time': received_time,
        'content': content,
        'folder': 'INBOX',
        'has_attachments': False
    }

def test_database_operations():
    """测试数据库操作性能"""
    print("=" * 60)
    print("测试数据库操作性能")
    print("=" * 60)
    
    db = Database()
    email_id = 1  # 假设邮箱ID为1
    
    # 生成测试数据
    test_mails = []
    for i in range(100):
        test_mails.append(generate_test_mail(i))
    
    print(f"生成了 {len(test_mails)} 封测试邮件")
    
    # 测试插入性能
    start_time = time.time()
    inserted_count = 0
    
    for mail in test_mails:
        success, mail_id = db.add_mail_record(
            email_id=email_id,
            subject=mail['subject'],
            sender=mail['sender'],
            received_time=mail['received_time'],
            content=mail['content'],
            folder=mail['folder'],
            has_attachments=0,
            message_id=mail['message_id'],
            server_uid=mail['server_uid']
        )
        if success:
            inserted_count += 1
    
    insert_time = time.time() - start_time
    print(f"插入 {inserted_count} 封邮件耗时: {insert_time:.3f}秒")
    print(f"平均每封邮件插入时间: {insert_time/len(test_mails)*1000:.2f}毫秒")
    
    # 测试重复检测性能
    start_time = time.time()
    duplicate_count = 0
    
    for mail in test_mails:
        success, mail_id = db.add_mail_record(
            email_id=email_id,
            subject=mail['subject'],
            sender=mail['sender'],
            received_time=mail['received_time'],
            content=mail['content'],
            folder=mail['folder'],
            has_attachments=0,
            message_id=mail['message_id'],
            server_uid=mail['server_uid']
        )
        if not success:
            duplicate_count += 1
    
    duplicate_check_time = time.time() - start_time
    print(f"重复检测 {duplicate_count} 封邮件耗时: {duplicate_check_time:.3f}秒")
    print(f"平均每封邮件重复检测时间: {duplicate_check_time/len(test_mails)*1000:.2f}毫秒")
    
    return inserted_count, duplicate_count

def test_batch_precheck():
    """测试批量预检查性能"""
    print("=" * 60)
    print("测试批量预检查性能")
    print("=" * 60)
    
    db = Database()
    precheck = MailPreChecker(db)
    email_id = 1
    
    # 生成测试数据（包含已存在和新邮件）
    test_mails = []
    
    # 添加一些已存在的邮件（使用之前插入的数据）
    for i in range(50):
        test_mails.append(generate_test_mail(i))
    
    # 添加一些新邮件
    for i in range(50, 100):
        test_mails.append(generate_test_mail(i))
    
    print(f"生成了 {len(test_mails)} 封测试邮件用于预检查")
    
    # 测试批量预检查性能
    start_time = time.time()
    new_mails = precheck.filter_new_mails(email_id, test_mails)
    precheck_time = time.time() - start_time
    
    print(f"批量预检查 {len(test_mails)} 封邮件耗时: {precheck_time:.3f}秒")
    print(f"平均每封邮件预检查时间: {precheck_time/len(test_mails)*1000:.2f}毫秒")
    print(f"过滤出新邮件: {len(new_mails)} 封")
    print(f"跳过已存在邮件: {len(test_mails) - len(new_mails)} 封")
    
    return len(new_mails), len(test_mails) - len(new_mails)

def test_identifier_priority():
    """测试标识符优先级"""
    print("=" * 60)
    print("测试标识符优先级")
    print("=" * 60)
    
    db = Database()
    email_id = 1
    
    # 创建测试邮件
    base_mail = generate_test_mail(1000)
    
    # 测试1: 只有Message-ID的邮件
    mail1 = base_mail.copy()
    mail1['server_uid'] = None
    success1, _ = db.add_mail_record(
        email_id=email_id,
        subject=mail1['subject'],
        sender=mail1['sender'],
        received_time=mail1['received_time'],
        content=mail1['content'],
        message_id=mail1['message_id'],
        server_uid=mail1['server_uid']
    )
    print(f"插入只有Message-ID的邮件: {'成功' if success1 else '失败'}")
    
    # 测试2: 尝试插入相同Message-ID的邮件（应该被拒绝）
    mail2 = base_mail.copy()
    mail2['subject'] = "不同的主题"
    mail2['server_uid'] = "99999"  # 不同的Server-UID
    success2, _ = db.add_mail_record(
        email_id=email_id,
        subject=mail2['subject'],
        sender=mail2['sender'],
        received_time=mail2['received_time'],
        content=mail2['content'],
        message_id=mail2['message_id'],
        server_uid=mail2['server_uid']
    )
    print(f"插入相同Message-ID但不同Server-UID的邮件: {'成功' if success2 else '失败（预期）'}")
    
    # 测试3: 只有Server-UID的邮件
    mail3 = generate_test_mail(1001)
    mail3['message_id'] = None
    success3, _ = db.add_mail_record(
        email_id=email_id,
        subject=mail3['subject'],
        sender=mail3['sender'],
        received_time=mail3['received_time'],
        content=mail3['content'],
        message_id=mail3['message_id'],
        server_uid=mail3['server_uid']
    )
    print(f"插入只有Server-UID的邮件: {'成功' if success3 else '失败'}")
    
    # 测试4: 尝试插入相同Server-UID的邮件（应该被拒绝）
    mail4 = mail3.copy()
    mail4['subject'] = "另一个不同的主题"
    success4, _ = db.add_mail_record(
        email_id=email_id,
        subject=mail4['subject'],
        sender=mail4['sender'],
        received_time=mail4['received_time'],
        content=mail4['content'],
        message_id=mail4['message_id'],
        server_uid=mail4['server_uid']
    )
    print(f"插入相同Server-UID的邮件: {'成功' if success4 else '失败（预期）'}")

def test_hash_fallback():
    """测试哈希兜底机制"""
    print("=" * 60)
    print("测试哈希兜底机制")
    print("=" * 60)
    
    db = Database()
    email_id = 1
    
    # 创建没有Message-ID和Server-UID的邮件
    mail1 = {
        'subject': '测试哈希兜底邮件',
        'sender': '<EMAIL>',
        'received_time': get_china_now(),
        'content': '这是用于测试哈希兜底机制的邮件',
        'folder': 'INBOX',
        'message_id': None,
        'server_uid': None
    }
    
    success1, _ = db.add_mail_record(
        email_id=email_id,
        subject=mail1['subject'],
        sender=mail1['sender'],
        received_time=mail1['received_time'],
        content=mail1['content'],
        message_id=mail1['message_id'],
        server_uid=mail1['server_uid']
    )
    print(f"插入没有标识符的邮件: {'成功' if success1 else '失败'}")
    
    # 尝试插入相同内容的邮件（应该被哈希检测拒绝）
    mail2 = mail1.copy()
    success2, _ = db.add_mail_record(
        email_id=email_id,
        subject=mail2['subject'],
        sender=mail2['sender'],
        received_time=mail2['received_time'],
        content=mail2['content'],
        message_id=mail2['message_id'],
        server_uid=mail2['server_uid']
    )
    print(f"插入相同内容的邮件: {'成功' if success2 else '失败（预期，哈希匹配）'}")

def main():
    """主测试函数"""
    print("开始测试优化后的邮件同步机制")
    print("=" * 60)
    
    try:
        # 测试数据库操作
        inserted, duplicates = test_database_operations()
        
        # 测试批量预检查
        new_mails, existing_mails = test_batch_precheck()
        
        # 测试标识符优先级
        test_identifier_priority()
        
        # 测试哈希兜底机制
        test_hash_fallback()
        
        print("=" * 60)
        print("测试总结:")
        print(f"- 数据库操作: 插入 {inserted} 封，检测重复 {duplicates} 封")
        print(f"- 批量预检查: 新邮件 {new_mails} 封，已存在 {existing_mails} 封")
        print("- 标识符优先级测试完成")
        print("- 哈希兜底机制测试完成")
        print("=" * 60)
        print("所有测试完成！")
        
    except Exception as e:
        logger.error(f"测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
